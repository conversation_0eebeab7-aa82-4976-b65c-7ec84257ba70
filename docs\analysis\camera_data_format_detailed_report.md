# 摄像头数据格式详细分析报告

## 摄像头设备信息
**设备类型：** MaixCam（基于注释信息）
**通信接口：** USART6
**通信参数：** 115200波特率，8数据位，1停止位，无校验

## 数据格式规范

### 1. 基本数据格式

单片机解析的摄像头数据格式为**ASCII文本格式**，采用**行分割**的结构化数据：

```
数据格式1：red:(x,y)\n
数据格式2：bp:(x,y)\n
```

### 2. 详细格式说明

#### 2.1 红色激光点数据格式
```
格式：red:(x,y)\n
示例：red:(150,200)\n
说明：
- "red:" - 固定前缀，标识红色激光点数据
- "(" - 左括号，坐标开始标记
- "x" - X坐标值（整数）
- "," - 逗号分隔符
- "y" - Y坐标值（整数）
- ")" - 右括号，坐标结束标记
- "\n" - 换行符，数据包结束标记
```

#### 2.2 蓝色激光点数据格式
```
格式：bp:(x,y)\n
示例：bp:(320,240)\n
说明：
- "bp:" - 固定前缀，标识蓝色激光点数据
- 其余格式与红色激光点相同
```

### 3. 数据解析实现

#### 3.1 解析函数位置
- **文件位置：** `bsp/pi_bsp.c`
- **函数名称：** `pi_parse_data(char *buffer)`
- **调用位置：** `uart_proc()` 函数中

#### 3.2 解析算法流程

```c
int pi_parse_data(char *buffer)
{
    // 1. 空指针检查
    if (!buffer) return -1;
    
    int parsed_x, parsed_y;
    int parsed_count;
    
    // 2. 红色激光点数据识别与解析
    if (strncmp(buffer, "red:", 4) == 0)
    {
        // 使用sscanf提取坐标
        parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2) return -2; // 解析失败
        
        // 更新全局数据结构
        latest_red_laser_coord.x = parsed_x;
        latest_red_laser_coord.y = parsed_y;
        latest_red_laser_coord.isValid = 1;
    }
    // 3. 蓝色激光点数据识别与解析
    else if (strncmp(buffer, "bp:", 3) == 0)
    {
        // 使用sscanf提取坐标
        parsed_count = sscanf(buffer, "bp:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2) return -2; // 解析失败
        
        // 更新全局数据结构
        latest_bp_laser_coord.x = parsed_x;
        latest_bp_laser_coord.y = parsed_y;
        latest_bp_laser_coord.isValid = 1;
    }
    // 4. 未知格式处理
    else
    {
        return -3; // 未知或无效格式
    }
    
    return 0; // 解析成功
}
```

### 4. 数据存储结构

#### 4.1 激光坐标数据结构
```c
typedef struct {
    char type;        // 激光类型: 'R'表示红色激光，'B'表示蓝色激光
    int x;           // X坐标（像素）
    int y;           // Y坐标（像素）
    uint8_t isValid; // 数据有效标志（0=无效，1=有效）
} LaserCoord_t;
```

#### 4.2 全局数据变量
```c
// 红色激光点坐标（默认值：x=0, y=0, 无效）
LaserCoord_t latest_red_laser_coord = {RED_LASER_ID, 0, 0, 0};

// 蓝色激光点坐标（默认值：x=150, y=95, 无效）
LaserCoord_t latest_bp_laser_coord = {BP_LASER_ID, 150, 95, 0};
```

### 5. 数据接收与处理流程

#### 5.1 完整数据流
```
MaixCam → USART6 → DMA → pi_rx_buf[64] → 中断触发 → 
环形缓冲区(ringbuffer_pi) → uart_proc() → 行缓冲区 → 
pi_parse_data() → 全局坐标变量 → pi_proc()控制应用
```

#### 5.2 行分割处理
```c
// 在uart_proc()中的行分割逻辑
for (int i = 0; i < length_pi; i++)
{
    char current_char = output_buffer_pi[i];
    
    // 添加字符到行缓冲区
    if (line_buffer_idx < sizeof(line_buffer) - 1)
    {
        line_buffer[line_buffer_idx++] = current_char;
    }
    
    // 检测到换行符，处理完整行
    if (current_char == '\n')
    {
        line_buffer[line_buffer_idx] = '\0'; // 添加字符串结束符
        
        // 调用解析函数
        int result = pi_parse_data(line_buffer);
        
        // 重置行缓冲区
        line_buffer_idx = 0;
    }
}
```

### 6. 错误处理机制

#### 6.1 解析错误代码
- **-1：** 空指针错误
- **-2：** 坐标解析失败（sscanf返回值不等于2）
- **-3：** 未知数据格式（既不是"red:"也不是"bp:"）

#### 6.2 缓冲区保护
- 行缓冲区大小：128字节
- 溢出保护：超出缓冲区时自动重置
- 错误日志：输出详细错误信息

### 7. 数据应用

#### 7.1 控制算法
```c
void pi_proc(void)
{
    float pos_out_x, pos_out_y = 0;
    
    // PID控制计算
    pos_out_x = pid_calc(&pid_x, latest_bp_laser_coord.x, latest_red_laser_coord.x, 0);
    pos_out_y = pid_calc(&pid_y, latest_bp_laser_coord.y, latest_red_laser_coord.y, 0);
    
    // 步进电机控制
    Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
}
```

#### 7.2 调度频率
- **数据接收处理：** 1ms周期（uart_proc）
- **控制算法执行：** 20ms周期（pi_proc）

### 8. 数据格式特点总结

#### 8.1 优点
- **简单易解析：** ASCII文本格式，便于调试
- **结构清晰：** 固定前缀+括号坐标格式
- **容错性好：** 行分割处理，单行错误不影响后续数据
- **实时性强：** 1ms处理周期，响应及时

#### 8.2 局限性
- **无校验机制：** 缺少数据完整性校验
- **无时间戳：** 无法判断数据新旧程度
- **坐标范围未限制：** 缺少合理性检查

### 9. 数据示例

#### 9.1 正常数据流示例
```
接收数据流：
red:(320,240)\n
bp:(300,250)\n
red:(325,245)\n
bp:(305,255)\n

解析结果：
latest_red_laser_coord: {type='R', x=325, y=245, isValid=1}
latest_bp_laser_coord:  {type='B', x=305, y=255, isValid=1}
```

#### 9.2 错误数据处理示例
```
错误数据：green:(100,200)\n
处理结果：pi_parse_data返回-3（未知格式）
输出日志：pi_parse_data returned error -3 for line: 'green:(100,200)'
```

## 结论

单片机解析的摄像头数据格式为**ASCII文本的结构化坐标数据**，采用简单的前缀标识+括号坐标的格式，支持红色和蓝色两种激光点的坐标识别。整个解析系统设计合理，实现正确，能够满足实时控制的需求。
